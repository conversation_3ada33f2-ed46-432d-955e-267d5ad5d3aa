<?php
// app/Controllers/MTDReportsController.php

namespace App\Controllers;

use App\Models\MtdpModel;
use App\Models\MtdpSpaModel;
use App\Models\MtdpDipModel;
use App\Models\MtdpKraModel;
use App\Models\MtdpSpecificAreaModel;
use App\Models\MtdpInvestmentsModel;
use App\Models\MtdpStrategiesModel;
use App\Models\MtdpIndicatorsModel;
use App\Models\WorkplanMtdpLinkModel;
use App\Models\ProposalModel;
use App\Models\WorkplanActivityModel;
use CodeIgniter\Controller;

class MTDReportsController extends Controller
{
    /**
     * Display the MTDP Plans Report (Read-only)
     */
    public function index()
    {
        $mtdpModel = new MtdpModel();
        $spaModel = new MtdpSpaModel();
        $dipModel = new MtdpDipModel();
        $kraModel = new MtdpKraModel();
        $saModel = new MtdpSpecificAreaModel();
        $investmentsModel = new MtdpInvestmentsModel();
        $strategiesModel = new MtdpStrategiesModel();
        $indicatorsModel = new MtdpIndicatorsModel();
        $workplanLinkModel = new WorkplanMtdpLinkModel();
        $proposalModel = new ProposalModel();
        $workplanActivityModel = new WorkplanActivityModel();

        // Get date filters from request
        $dateFrom = $this->request->getGet('date_from');
        $dateTo = $this->request->getGet('date_to');

        // Get all MTDP data with new filtering logic
        $plans = $mtdpModel->findAll();
        $spas = $this->getFilteredMtdpData($spaModel, $plans, $dateFrom, $dateTo);
        $dips = $this->getFilteredMtdpData($dipModel, $plans, $dateFrom, $dateTo);
        $kras = $this->getFilteredMtdpData($kraModel, $plans, $dateFrom, $dateTo);
        $specific_areas = $this->getFilteredMtdpData($saModel, $plans, $dateFrom, $dateTo);
        $investments = $this->getFilteredMtdpData($investmentsModel, $plans, $dateFrom, $dateTo);
        $strategies = $this->getFilteredMtdpData($strategiesModel, $plans, $dateFrom, $dateTo);
        $indicators = $this->getFilteredMtdpData($indicatorsModel, $plans, $dateFrom, $dateTo);

        // Get workplan counts based on completed proposals within date range
        $workplanCounts = $this->getWorkplanCountsByCompletedProposals($workplanLinkModel, $proposalModel, $dateFrom, $dateTo);

        // Get comprehensive performance analytics
        $performanceAnalytics = $this->getPerformanceAnalytics($workplanLinkModel, $workplanActivityModel, $proposalModel, $dateFrom, $dateTo);

        // Prepare chart data with performance metrics
        $chartData = $this->prepareChartData($plans, $spas, $dips, $kras, $specific_areas, $investments, $strategies, $indicators, $dateFrom, $dateTo);
        $chartData = array_merge($chartData, $this->preparePerformanceChartData($performanceAnalytics));

        // Pass all data to the new performance-focused view
        return view('reports_mtdp/reports_mtdp_performance', [
            'title' => 'MTDP Performance Analytics Dashboard',
            'plans' => $plans,
            'spas' => $spas,
            'dips' => $dips,
            'kras' => $kras,
            'specific_areas' => $specific_areas,
            'investments' => $investments,
            'strategies' => $strategies,
            'indicators' => $indicators,
            'chartData' => $chartData,
            'workplanCounts' => $workplanCounts,
            'performanceAnalytics' => $performanceAnalytics,
            'dateFrom' => $dateFrom,
            'dateTo' => $dateTo,
        ]);
    }

    /**
     * Prepare data for charts and graphs
     */
    private function prepareChartData($plans, $spas, $dips, $kras, $specific_areas, $investments, $strategies, $indicators, $dateFrom = null, $dateTo = null)
    {
        $chartData = [];

        // 1. Investment distribution by year
        $yearlyInvestments = [
            'year_one' => 0,
            'year_two' => 0,
            'year_three' => 0,
            'year_four' => 0,
            'year_five' => 0
        ];

        foreach ($investments as $investment) {
            $yearlyInvestments['year_one'] += (float)($investment['year_one'] ?? 0);
            $yearlyInvestments['year_two'] += (float)($investment['year_two'] ?? 0);
            $yearlyInvestments['year_three'] += (float)($investment['year_three'] ?? 0);
            $yearlyInvestments['year_four'] += (float)($investment['year_four'] ?? 0);
            $yearlyInvestments['year_five'] += (float)($investment['year_five'] ?? 0);
        }

        $chartData['yearlyInvestments'] = $yearlyInvestments;

        // 2. Investment distribution by DIP
        $dipInvestments = [];
        foreach ($dips as $dip) {
            $dipInvestments[$dip['id']] = [
                'title' => $dip['dip_title'],
                'total' => 0
            ];
        }

        foreach ($investments as $investment) {
            if (isset($dipInvestments[$investment['dip_id']])) {
                $total = (float)($investment['year_one'] ?? 0) +
                         (float)($investment['year_two'] ?? 0) +
                         (float)($investment['year_three'] ?? 0) +
                         (float)($investment['year_four'] ?? 0) +
                         (float)($investment['year_five'] ?? 0);
                $dipInvestments[$investment['dip_id']]['total'] += $total;
            }
        }

        $chartData['dipInvestments'] = $dipInvestments;

        // 3. Status distribution
        $statusCounts = [
            'dips' => ['active' => 0, 'inactive' => 0],
            'kras' => ['active' => 0, 'inactive' => 0],
            'specific_areas' => ['active' => 0, 'inactive' => 0],
            'strategies' => ['active' => 0, 'inactive' => 0],
            'indicators' => ['active' => 0, 'inactive' => 0]
        ];

        foreach ($dips as $dip) {
            $statusCounts['dips'][$dip['dip_status'] == 1 ? 'active' : 'inactive']++;
        }

        foreach ($kras as $kra) {
            $statusCounts['kras'][$kra['kra_status'] == 1 ? 'active' : 'inactive']++;
        }

        foreach ($specific_areas as $sa) {
            $statusCounts['specific_areas'][$sa['sa_status'] == 1 ? 'active' : 'inactive']++;
        }

        foreach ($strategies as $strategy) {
            $statusCounts['strategies'][$strategy['strategies_status'] == 1 ? 'active' : 'inactive']++;
        }

        foreach ($indicators as $indicator) {
            $statusCounts['indicators'][$indicator['indicators_status'] == 1 ? 'active' : 'inactive']++;
        }

        $chartData['statusCounts'] = $statusCounts;

        // 4. Count entities by MTDP plan
        $entitiesByPlan = [];
        foreach ($plans as $plan) {
            $entitiesByPlan[$plan['id']] = [
                'title' => $plan['title'],
                'spas' => 0,
                'dips' => 0,
                'kras' => 0,
                'specific_areas' => 0,
                'investments' => 0,
                'strategies' => 0,
                'indicators' => 0
            ];
        }

        foreach ($spas as $spa) {
            if (isset($entitiesByPlan[$spa['mtdp_id']])) {
                $entitiesByPlan[$spa['mtdp_id']]['spas']++;
            }
        }

        foreach ($dips as $dip) {
            if (isset($entitiesByPlan[$dip['mtdp_id']])) {
                $entitiesByPlan[$dip['mtdp_id']]['dips']++;
            }
        }

        foreach ($kras as $kra) {
            if (isset($entitiesByPlan[$kra['mtdp_id']])) {
                $entitiesByPlan[$kra['mtdp_id']]['kras']++;
            }
        }

        foreach ($specific_areas as $sa) {
            if (isset($entitiesByPlan[$sa['mtdp_id']])) {
                $entitiesByPlan[$sa['mtdp_id']]['specific_areas']++;
            }
        }

        foreach ($investments as $investment) {
            if (isset($entitiesByPlan[$investment['mtdp_id']])) {
                $entitiesByPlan[$investment['mtdp_id']]['investments']++;
            }
        }

        foreach ($strategies as $strategy) {
            if (isset($entitiesByPlan[$strategy['mtdp_id']])) {
                $entitiesByPlan[$strategy['mtdp_id']]['strategies']++;
            }
        }

        foreach ($indicators as $indicator) {
            if (isset($entitiesByPlan[$indicator['mtdp_id']])) {
                $entitiesByPlan[$indicator['mtdp_id']]['indicators']++;
            }
        }

        $chartData['entitiesByPlan'] = $entitiesByPlan;

        return $chartData;
    }




    /**
     * Get filtered MTDP data based on plan year ranges and date filter
     */
    private function getFilteredMtdpData($model, $plans, $dateFrom = null, $dateTo = null)
    {
        // If no date filter is provided, return all data
        if (!$dateFrom && !$dateTo) {
            return $model->findAll();
        }

        // Get all data first
        $allData = $model->findAll();
        $filteredData = [];

        foreach ($allData as $item) {
            // Find the corresponding MTDP plan
            $mtdpPlan = null;
            foreach ($plans as $plan) {
                if ($plan['id'] == $item['mtdp_id']) {
                    $mtdpPlan = $plan;
                    break;
                }
            }

            if (!$mtdpPlan || !$mtdpPlan['date_from'] || !$mtdpPlan['date_to']) {
                continue;
            }

            // Calculate year ranges based on MTDP plan dates
            $planStartYear = (int)date('Y', strtotime($mtdpPlan['date_from']));
            $planEndYear = (int)date('Y', strtotime($mtdpPlan['date_to']));

            // Check if the filter date range overlaps with any of the plan years
            if ($this->dateRangeOverlapsWithPlanYears($dateFrom, $dateTo, $planStartYear, $planEndYear)) {
                $filteredData[] = $item;
            }
        }

        return $filteredData;
    }

    /**
     * Check if date range overlaps with MTDP plan years
     */
    private function dateRangeOverlapsWithPlanYears($dateFrom, $dateTo, $planStartYear, $planEndYear)
    {
        $filterStartYear = $dateFrom ? (int)date('Y', strtotime($dateFrom)) : $planStartYear;
        $filterEndYear = $dateTo ? (int)date('Y', strtotime($dateTo)) : $planEndYear;

        // Check if there's any overlap between filter years and plan years
        return !($filterEndYear < $planStartYear || $filterStartYear > $planEndYear);
    }

    /**
     * Get workplan counts based on completed proposals within date range
     */
    private function getWorkplanCountsByCompletedProposals($workplanLinkModel, $proposalModel, $dateFrom = null, $dateTo = null)
    {
        $counts = [
            'strategies' => [],
            'kras' => [],
            'dips' => [],
            'spas' => [],
            'specific_areas' => [],
            'investments' => [],
            'mtdp_plans' => []
        ];

        // If no date filter, use the old method for backward compatibility
        if (!$dateFrom && !$dateTo) {
            return $this->getWorkplanCountsLegacy($workplanLinkModel);
        }

        // Get completed proposals within the date range
        // Completed activities are those with status 'approved' or 'rated'
        $proposalBuilder = $proposalModel->builder();
        $proposalBuilder->select('workplan_id, activity_id, status_at, rated_at')
                       ->whereIn('status', ['approved', 'rated']); // Include both approved and rated as completed

        if ($dateFrom && $dateTo) {
            // Use status_at for approved proposals and rated_at for rated proposals
            $proposalBuilder->groupStart()
                           ->where('(status = "approved" AND status_at >=', $dateFrom)
                           ->where('status_at <=', $dateTo . ' 23:59:59)')
                           ->orGroupStart()
                           ->where('status = "rated" AND rated_at >=', $dateFrom)
                           ->where('rated_at <=', $dateTo . ' 23:59:59')
                           ->groupEnd()
                           ->groupEnd();
        } elseif ($dateFrom) {
            $proposalBuilder->groupStart()
                           ->where('(status = "approved" AND status_at >=', $dateFrom)
                           ->orWhere('(status = "rated" AND rated_at >=', $dateFrom)
                           ->groupEnd();
        } elseif ($dateTo) {
            $proposalBuilder->groupStart()
                           ->where('(status = "approved" AND status_at <=', $dateTo . ' 23:59:59)')
                           ->orWhere('(status = "rated" AND rated_at <=', $dateTo . ' 23:59:59)')
                           ->groupEnd();
        }

        $completedProposals = $proposalBuilder->get()->getResultArray();

        // Extract workplan activity IDs from completed proposals
        $completedActivityIds = array_column($completedProposals, 'activity_id');

        if (empty($completedActivityIds)) {
            return $counts; // Return empty counts if no completed proposals
        }

        // Get all workplan links for these completed activities with full hierarchy information
        $linkBuilder = $workplanLinkModel->builder();
        $linkBuilder->select('workplan_activity_id, mtdp_id, spa_id, dip_id, sa_id, investment_id, kra_id, strategies_id')
                   ->whereIn('workplan_activity_id', $completedActivityIds);
        $allLinks = $linkBuilder->get()->getResultArray();

        // Step 1: Count activities directly linked to strategies (bottom level)
        foreach ($allLinks as $link) {
            if (!empty($link['strategies_id'])) {
                if (!isset($counts['strategies'][$link['strategies_id']])) {
                    $counts['strategies'][$link['strategies_id']] = 0;
                }
                $counts['strategies'][$link['strategies_id']]++;
            }
        }

        // Step 2: Cumulative count for KRAs (sum from all linked strategies)
        $strategiesModel = new \App\Models\MtdpStrategiesModel();
        $allStrategies = $strategiesModel->findAll();

        foreach ($allStrategies as $strategy) {
            if (isset($counts['strategies'][$strategy['id']]) && !empty($strategy['kra_id'])) {
                if (!isset($counts['kras'][$strategy['kra_id']])) {
                    $counts['kras'][$strategy['kra_id']] = 0;
                }
                $counts['kras'][$strategy['kra_id']] += $counts['strategies'][$strategy['id']];
            }
        }

        // Step 3: Cumulative count for Investments (sum from all linked KRAs)
        $kraModel = new \App\Models\MtdpKraModel();
        $allKras = $kraModel->findAll();

        foreach ($allKras as $kra) {
            if (isset($counts['kras'][$kra['id']]) && !empty($kra['investment_id'])) {
                if (!isset($counts['investments'][$kra['investment_id']])) {
                    $counts['investments'][$kra['investment_id']] = 0;
                }
                $counts['investments'][$kra['investment_id']] += $counts['kras'][$kra['id']];
            }
        }

        // Step 4: Cumulative count for Specific Areas (sum from all linked Investments)
        $investmentsModel = new \App\Models\MtdpInvestmentsModel();
        $allInvestments = $investmentsModel->findAll();

        foreach ($allInvestments as $investment) {
            if (isset($counts['investments'][$investment['id']]) && !empty($investment['sa_id'])) {
                if (!isset($counts['specific_areas'][$investment['sa_id']])) {
                    $counts['specific_areas'][$investment['sa_id']] = 0;
                }
                $counts['specific_areas'][$investment['sa_id']] += $counts['investments'][$investment['id']];
            }
        }

        // Step 5: Cumulative count for DIPs (sum from all linked Specific Areas)
        $saModel = new \App\Models\MtdpSpecificAreaModel();
        $allSpecificAreas = $saModel->findAll();

        foreach ($allSpecificAreas as $sa) {
            if (isset($counts['specific_areas'][$sa['id']]) && !empty($sa['dip_id'])) {
                if (!isset($counts['dips'][$sa['dip_id']])) {
                    $counts['dips'][$sa['dip_id']] = 0;
                }
                $counts['dips'][$sa['dip_id']] += $counts['specific_areas'][$sa['id']];
            }
        }

        // Step 6: Cumulative count for SPAs (sum from all linked DIPs)
        $dipModel = new \App\Models\MtdpDipModel();
        $allDips = $dipModel->findAll();

        foreach ($allDips as $dip) {
            if (isset($counts['dips'][$dip['id']]) && !empty($dip['spa_id'])) {
                if (!isset($counts['spas'][$dip['spa_id']])) {
                    $counts['spas'][$dip['spa_id']] = 0;
                }
                $counts['spas'][$dip['spa_id']] += $counts['dips'][$dip['id']];
            }
        }

        // Step 7: Cumulative count for MTDP Plans (sum from all linked SPAs)
        $spaModel = new \App\Models\MtdpSpaModel();
        $allSpas = $spaModel->findAll();

        foreach ($allSpas as $spa) {
            if (isset($counts['spas'][$spa['id']]) && !empty($spa['mtdp_id'])) {
                if (!isset($counts['mtdp_plans'][$spa['mtdp_id']])) {
                    $counts['mtdp_plans'][$spa['mtdp_id']] = 0;
                }
                $counts['mtdp_plans'][$spa['mtdp_id']] += $counts['spas'][$spa['id']];
            }
        }

        return $counts;
    }

    /**
     * Legacy workplan counts method (for backward compatibility when no date filter)
     */
    private function getWorkplanCountsLegacy($workplanLinkModel)
    {
        $counts = [
            'strategies' => [],
            'kras' => [],
            'dips' => [],
            'spas' => [],
            'specific_areas' => [],
            'investments' => [],
            'mtdp_plans' => []
        ];

        // Get all workplan links with full hierarchy information
        $allLinks = $workplanLinkModel->builder()
                                     ->select('workplan_activity_id, mtdp_id, spa_id, dip_id, sa_id, investment_id, kra_id, strategies_id')
                                     ->get()->getResultArray();

        // Step 1: Count activities directly linked to strategies (bottom level)
        foreach ($allLinks as $link) {
            if (!empty($link['strategies_id'])) {
                if (!isset($counts['strategies'][$link['strategies_id']])) {
                    $counts['strategies'][$link['strategies_id']] = 0;
                }
                $counts['strategies'][$link['strategies_id']]++;
            }
        }

        // Step 2: Cumulative count for KRAs (sum from all linked strategies)
        $strategiesModel = new \App\Models\MtdpStrategiesModel();
        $allStrategies = $strategiesModel->findAll();

        foreach ($allStrategies as $strategy) {
            if (isset($counts['strategies'][$strategy['id']]) && !empty($strategy['kra_id'])) {
                if (!isset($counts['kras'][$strategy['kra_id']])) {
                    $counts['kras'][$strategy['kra_id']] = 0;
                }
                $counts['kras'][$strategy['kra_id']] += $counts['strategies'][$strategy['id']];
            }
        }

        // Step 3: Cumulative count for Investments (sum from all linked KRAs)
        $kraModel = new \App\Models\MtdpKraModel();
        $allKras = $kraModel->findAll();

        foreach ($allKras as $kra) {
            if (isset($counts['kras'][$kra['id']]) && !empty($kra['investment_id'])) {
                if (!isset($counts['investments'][$kra['investment_id']])) {
                    $counts['investments'][$kra['investment_id']] = 0;
                }
                $counts['investments'][$kra['investment_id']] += $counts['kras'][$kra['id']];
            }
        }

        // Step 4: Cumulative count for Specific Areas (sum from all linked Investments)
        $investmentsModel = new \App\Models\MtdpInvestmentsModel();
        $allInvestments = $investmentsModel->findAll();

        foreach ($allInvestments as $investment) {
            if (isset($counts['investments'][$investment['id']]) && !empty($investment['sa_id'])) {
                if (!isset($counts['specific_areas'][$investment['sa_id']])) {
                    $counts['specific_areas'][$investment['sa_id']] = 0;
                }
                $counts['specific_areas'][$investment['sa_id']] += $counts['investments'][$investment['id']];
            }
        }

        // Step 5: Cumulative count for DIPs (sum from all linked Specific Areas)
        $saModel = new \App\Models\MtdpSpecificAreaModel();
        $allSpecificAreas = $saModel->findAll();

        foreach ($allSpecificAreas as $sa) {
            if (isset($counts['specific_areas'][$sa['id']]) && !empty($sa['dip_id'])) {
                if (!isset($counts['dips'][$sa['dip_id']])) {
                    $counts['dips'][$sa['dip_id']] = 0;
                }
                $counts['dips'][$sa['dip_id']] += $counts['specific_areas'][$sa['id']];
            }
        }

        // Step 6: Cumulative count for SPAs (sum from all linked DIPs)
        $dipModel = new \App\Models\MtdpDipModel();
        $allDips = $dipModel->findAll();

        foreach ($allDips as $dip) {
            if (isset($counts['dips'][$dip['id']]) && !empty($dip['spa_id'])) {
                if (!isset($counts['spas'][$dip['spa_id']])) {
                    $counts['spas'][$dip['spa_id']] = 0;
                }
                $counts['spas'][$dip['spa_id']] += $counts['dips'][$dip['id']];
            }
        }

        // Step 7: Cumulative count for MTDP Plans (sum from all linked SPAs)
        $spaModel = new \App\Models\MtdpSpaModel();
        $allSpas = $spaModel->findAll();

        foreach ($allSpas as $spa) {
            if (isset($counts['spas'][$spa['id']]) && !empty($spa['mtdp_id'])) {
                if (!isset($counts['mtdp_plans'][$spa['mtdp_id']])) {
                    $counts['mtdp_plans'][$spa['mtdp_id']] = 0;
                }
                $counts['mtdp_plans'][$spa['mtdp_id']] += $counts['spas'][$spa['id']];
            }
        }

        return $counts;
    }

    /**
     * Get comprehensive performance analytics for MTDP components
     */
    private function getPerformanceAnalytics($workplanLinkModel, $workplanActivityModel, $proposalModel, $dateFrom = null, $dateTo = null)
    {
        $analytics = [
            'activity_linkages' => [],
            'financial_performance' => [],
            'performance_metrics' => [],
            'strategic_performance' => []
        ];

        // Get all workplan links
        $allLinks = $workplanLinkModel->findAll();

        // Get all activities
        $allActivities = $workplanActivityModel->findAll();

        // Get all proposals with date filtering if needed
        if ($dateFrom || $dateTo) {
            $proposalBuilder = $proposalModel->builder();
            $proposalBuilder->whereIn('status', ['approved', 'rated']);

            if ($dateFrom && $dateTo) {
                $proposalBuilder->groupStart()
                               ->where('(status = "approved" AND status_at >=', $dateFrom)
                               ->where('status_at <=', $dateTo . ' 23:59:59)')
                               ->orGroupStart()
                               ->where('status = "rated" AND rated_at >=', $dateFrom)
                               ->where('rated_at <=', $dateTo . ' 23:59:59')
                               ->groupEnd()
                               ->groupEnd();
            } elseif ($dateFrom) {
                $proposalBuilder->groupStart()
                               ->where('(status = "approved" AND status_at >=', $dateFrom)
                               ->orWhere('(status = "rated" AND rated_at >=', $dateFrom . ')')
                               ->groupEnd();
            } elseif ($dateTo) {
                $proposalBuilder->groupStart()
                               ->where('(status = "approved" AND status_at <=', $dateTo . ' 23:59:59)')
                               ->orWhere('(status = "rated" AND rated_at <=', $dateTo . ' 23:59:59)')
                               ->groupEnd();
            }

            $filteredProposals = $proposalBuilder->findAll();
            $filteredActivityIds = array_column($filteredProposals, 'activity_id');
        } else {
            $filteredProposals = $proposalModel->findAll();
            $filteredActivityIds = array_column($filteredProposals, 'activity_id');
        }

        // Create lookup arrays for better performance
        $activitiesById = [];
        foreach ($allActivities as $activity) {
            $activitiesById[$activity['id']] = $activity;
        }

        $proposalsByActivityId = [];
        foreach ($filteredProposals as $proposal) {
            $proposalsByActivityId[$proposal['activity_id']] = $proposal;
        }

        // Build linked activities array with combined data
        $linkedActivities = [];
        foreach ($allLinks as $link) {
            $activityId = $link['workplan_activity_id'];

            // Skip if activity not in filtered list (when date filtering is applied)
            if (!empty($filteredActivityIds) && !in_array($activityId, $filteredActivityIds)) {
                continue;
            }

            $activity = $activitiesById[$activityId] ?? null;
            $proposal = $proposalsByActivityId[$activityId] ?? null;

            if ($activity) {
                $linkedActivities[] = array_merge($link, [
                    'activity_id' => $activity['id'],
                    'activity_title' => $activity['title'],
                    'activity_type' => $activity['activity_type'],
                    'q_one' => $activity['q_one'] ?? 0,
                    'q_two' => $activity['q_two'] ?? 0,
                    'q_three' => $activity['q_three'] ?? 0,
                    'q_four' => $activity['q_four'] ?? 0,
                    'total_cost' => $activity['total_cost'] ?? 0,
                    'activity_status' => $activity['status'],
                    'rating_score' => $proposal['rating_score'] ?? 0,
                    'proposal_status' => $proposal['status'] ?? null,
                    'rated_at' => $proposal['rated_at'] ?? null,
                    'status_at' => $proposal['status_at'] ?? null
                ]);
            }
        }

        // Process activity linkages and performance data
        $this->processActivityLinkages($linkedActivities, $analytics);
        $this->processFinancialPerformance($linkedActivities, $analytics);
        $this->processPerformanceMetrics($linkedActivities, $analytics);
        $this->processStrategicPerformance($linkedActivities, $analytics);

        return $analytics;
    }

    /**
     * Process activity linkage information
     */
    private function processActivityLinkages($linkedActivities, &$analytics)
    {
        $linkages = [
            'mtdp_plans' => [],
            'spas' => [],
            'dips' => [],
            'specific_areas' => [],
            'investments' => [],
            'kras' => [],
            'strategies' => []
        ];

        foreach ($linkedActivities as $activity) {
            // Count activities linked to each MTDP component
            if (!empty($activity['mtdp_id'])) {
                if (!isset($linkages['mtdp_plans'][$activity['mtdp_id']])) {
                    $linkages['mtdp_plans'][$activity['mtdp_id']] = [
                        'count' => 0,
                        'activities' => []
                    ];
                }
                $linkages['mtdp_plans'][$activity['mtdp_id']]['count']++;
                $linkages['mtdp_plans'][$activity['mtdp_id']]['activities'][] = $activity;
            }

            if (!empty($activity['spa_id'])) {
                if (!isset($linkages['spas'][$activity['spa_id']])) {
                    $linkages['spas'][$activity['spa_id']] = [
                        'count' => 0,
                        'activities' => []
                    ];
                }
                $linkages['spas'][$activity['spa_id']]['count']++;
                $linkages['spas'][$activity['spa_id']]['activities'][] = $activity;
            }

            if (!empty($activity['dip_id'])) {
                if (!isset($linkages['dips'][$activity['dip_id']])) {
                    $linkages['dips'][$activity['dip_id']] = [
                        'count' => 0,
                        'activities' => []
                    ];
                }
                $linkages['dips'][$activity['dip_id']]['count']++;
                $linkages['dips'][$activity['dip_id']]['activities'][] = $activity;
            }

            if (!empty($activity['sa_id'])) {
                if (!isset($linkages['specific_areas'][$activity['sa_id']])) {
                    $linkages['specific_areas'][$activity['sa_id']] = [
                        'count' => 0,
                        'activities' => []
                    ];
                }
                $linkages['specific_areas'][$activity['sa_id']]['count']++;
                $linkages['specific_areas'][$activity['sa_id']]['activities'][] = $activity;
            }

            if (!empty($activity['investment_id'])) {
                if (!isset($linkages['investments'][$activity['investment_id']])) {
                    $linkages['investments'][$activity['investment_id']] = [
                        'count' => 0,
                        'activities' => []
                    ];
                }
                $linkages['investments'][$activity['investment_id']]['count']++;
                $linkages['investments'][$activity['investment_id']]['activities'][] = $activity;
            }

            if (!empty($activity['kra_id'])) {
                if (!isset($linkages['kras'][$activity['kra_id']])) {
                    $linkages['kras'][$activity['kra_id']] = [
                        'count' => 0,
                        'activities' => []
                    ];
                }
                $linkages['kras'][$activity['kra_id']]['count']++;
                $linkages['kras'][$activity['kra_id']]['activities'][] = $activity;
            }

            if (!empty($activity['strategies_id'])) {
                if (!isset($linkages['strategies'][$activity['strategies_id']])) {
                    $linkages['strategies'][$activity['strategies_id']] = [
                        'count' => 0,
                        'activities' => []
                    ];
                }
                $linkages['strategies'][$activity['strategies_id']]['count']++;
                $linkages['strategies'][$activity['strategies_id']]['activities'][] = $activity;
            }
        }

        $analytics['activity_linkages'] = $linkages;
    }

    /**
     * Process financial performance data
     */
    private function processFinancialPerformance($linkedActivities, &$analytics)
    {
        $financial = [
            'mtdp_plans' => [],
            'spas' => [],
            'dips' => [],
            'specific_areas' => [],
            'investments' => [],
            'kras' => [],
            'strategies' => []
        ];

        foreach ($linkedActivities as $activity) {
            $cost = (float)($activity['total_cost'] ?? 0);

            // Aggregate costs by MTDP component
            if (!empty($activity['mtdp_id'])) {
                if (!isset($financial['mtdp_plans'][$activity['mtdp_id']])) {
                    $financial['mtdp_plans'][$activity['mtdp_id']] = [
                        'total_cost' => 0,
                        'activity_count' => 0,
                        'avg_cost_per_activity' => 0
                    ];
                }
                $financial['mtdp_plans'][$activity['mtdp_id']]['total_cost'] += $cost;
                $financial['mtdp_plans'][$activity['mtdp_id']]['activity_count']++;
            }

            if (!empty($activity['spa_id'])) {
                if (!isset($financial['spas'][$activity['spa_id']])) {
                    $financial['spas'][$activity['spa_id']] = [
                        'total_cost' => 0,
                        'activity_count' => 0,
                        'avg_cost_per_activity' => 0
                    ];
                }
                $financial['spas'][$activity['spa_id']]['total_cost'] += $cost;
                $financial['spas'][$activity['spa_id']]['activity_count']++;
            }

            if (!empty($activity['dip_id'])) {
                if (!isset($financial['dips'][$activity['dip_id']])) {
                    $financial['dips'][$activity['dip_id']] = [
                        'total_cost' => 0,
                        'activity_count' => 0,
                        'avg_cost_per_activity' => 0
                    ];
                }
                $financial['dips'][$activity['dip_id']]['total_cost'] += $cost;
                $financial['dips'][$activity['dip_id']]['activity_count']++;
            }

            if (!empty($activity['sa_id'])) {
                if (!isset($financial['specific_areas'][$activity['sa_id']])) {
                    $financial['specific_areas'][$activity['sa_id']] = [
                        'total_cost' => 0,
                        'activity_count' => 0,
                        'avg_cost_per_activity' => 0
                    ];
                }
                $financial['specific_areas'][$activity['sa_id']]['total_cost'] += $cost;
                $financial['specific_areas'][$activity['sa_id']]['activity_count']++;
            }

            if (!empty($activity['investment_id'])) {
                if (!isset($financial['investments'][$activity['investment_id']])) {
                    $financial['investments'][$activity['investment_id']] = [
                        'total_cost' => 0,
                        'activity_count' => 0,
                        'avg_cost_per_activity' => 0
                    ];
                }
                $financial['investments'][$activity['investment_id']]['total_cost'] += $cost;
                $financial['investments'][$activity['investment_id']]['activity_count']++;
            }

            if (!empty($activity['kra_id'])) {
                if (!isset($financial['kras'][$activity['kra_id']])) {
                    $financial['kras'][$activity['kra_id']] = [
                        'total_cost' => 0,
                        'activity_count' => 0,
                        'avg_cost_per_activity' => 0
                    ];
                }
                $financial['kras'][$activity['kra_id']]['total_cost'] += $cost;
                $financial['kras'][$activity['kra_id']]['activity_count']++;
            }

            if (!empty($activity['strategies_id'])) {
                if (!isset($financial['strategies'][$activity['strategies_id']])) {
                    $financial['strategies'][$activity['strategies_id']] = [
                        'total_cost' => 0,
                        'activity_count' => 0,
                        'avg_cost_per_activity' => 0
                    ];
                }
                $financial['strategies'][$activity['strategies_id']]['total_cost'] += $cost;
                $financial['strategies'][$activity['strategies_id']]['activity_count']++;
            }
        }

        // Calculate average costs
        foreach ($financial as $componentType => &$components) {
            foreach ($components as &$component) {
                if ($component['activity_count'] > 0) {
                    $component['avg_cost_per_activity'] = $component['total_cost'] / $component['activity_count'];
                }
            }
        }

        $analytics['financial_performance'] = $financial;
    }

    /**
     * Process performance metrics (targets, achievements, ratings)
     */
    private function processPerformanceMetrics($linkedActivities, &$analytics)
    {
        $metrics = [
            'mtdp_plans' => [],
            'spas' => [],
            'dips' => [],
            'specific_areas' => [],
            'investments' => [],
            'kras' => [],
            'strategies' => []
        ];

        foreach ($linkedActivities as $activity) {
            // Calculate quarterly totals and achievements
            $totalTarget = (float)($activity['q_one'] ?? 0) +
                          (float)($activity['q_two'] ?? 0) +
                          (float)($activity['q_three'] ?? 0) +
                          (float)($activity['q_four'] ?? 0);

            // For achievements, we'll use the same fields as targets for now
            // In a real scenario, there would be separate achievement fields
            $totalAchieved = $totalTarget; // Placeholder - would need actual achievement data

            $rating = (float)($activity['rating_score'] ?? 0);

            // Calculate performance percentage
            $performancePercentage = $totalTarget > 0 ? ($totalAchieved / $totalTarget) * 100 : 0;

            // Process each MTDP component level
            $this->aggregateMetricsForComponent($metrics['mtdp_plans'], $activity['mtdp_id'],
                $totalTarget, $totalAchieved, $rating, $performancePercentage);

            $this->aggregateMetricsForComponent($metrics['spas'], $activity['spa_id'],
                $totalTarget, $totalAchieved, $rating, $performancePercentage);

            $this->aggregateMetricsForComponent($metrics['dips'], $activity['dip_id'],
                $totalTarget, $totalAchieved, $rating, $performancePercentage);

            $this->aggregateMetricsForComponent($metrics['specific_areas'], $activity['sa_id'],
                $totalTarget, $totalAchieved, $rating, $performancePercentage);

            $this->aggregateMetricsForComponent($metrics['investments'], $activity['investment_id'],
                $totalTarget, $totalAchieved, $rating, $performancePercentage);

            $this->aggregateMetricsForComponent($metrics['kras'], $activity['kra_id'],
                $totalTarget, $totalAchieved, $rating, $performancePercentage);

            $this->aggregateMetricsForComponent($metrics['strategies'], $activity['strategies_id'],
                $totalTarget, $totalAchieved, $rating, $performancePercentage);
        }

        // Calculate averages for all components
        foreach ($metrics as $componentType => &$components) {
            foreach ($components as &$component) {
                if ($component['activity_count'] > 0) {
                    $component['avg_rating'] = $component['total_rating'] / $component['activity_count'];
                    $component['avg_performance_percentage'] = $component['total_performance_percentage'] / $component['activity_count'];
                }
            }
        }

        $analytics['performance_metrics'] = $metrics;
    }

    /**
     * Helper method to aggregate metrics for a component
     */
    private function aggregateMetricsForComponent(&$components, $componentId, $target, $achieved, $rating, $performancePercentage)
    {
        if (empty($componentId)) return;

        if (!isset($components[$componentId])) {
            $components[$componentId] = [
                'total_target' => 0,
                'total_achieved' => 0,
                'total_rating' => 0,
                'total_performance_percentage' => 0,
                'activity_count' => 0,
                'avg_rating' => 0,
                'avg_performance_percentage' => 0
            ];
        }

        $components[$componentId]['total_target'] += $target;
        $components[$componentId]['total_achieved'] += $achieved;
        $components[$componentId]['total_rating'] += $rating;
        $components[$componentId]['total_performance_percentage'] += $performancePercentage;
        $components[$componentId]['activity_count']++;
    }

    /**
     * Process strategic performance analysis with cascading
     */
    private function processStrategicPerformance($linkedActivities, &$analytics)
    {
        // Load all MTDP models for cascading analysis
        $strategiesModel = new MtdpStrategiesModel();
        $kraModel = new MtdpKraModel();
        $investmentsModel = new MtdpInvestmentsModel();
        $saModel = new MtdpSpecificAreaModel();
        $dipModel = new MtdpDipModel();
        $spaModel = new MtdpSpaModel();

        $allStrategies = $strategiesModel->findAll();
        $allKras = $kraModel->findAll();
        $allInvestments = $investmentsModel->findAll();
        $allSpecificAreas = $saModel->findAll();
        $allDips = $dipModel->findAll();
        $allSpas = $spaModel->findAll();

        $strategic = [
            'strategies' => [],
            'kras' => [],
            'investments' => [],
            'specific_areas' => [],
            'dips' => [],
            'spas' => [],
            'mtdp_plans' => []
        ];

        // First, collect performance data at strategy level (bottom-up)
        foreach ($linkedActivities as $activity) {
            if (!empty($activity['strategies_id'])) {
                $strategyId = $activity['strategies_id'];
                if (!isset($strategic['strategies'][$strategyId])) {
                    $strategic['strategies'][$strategyId] = [
                        'performance_score' => 0,
                        'total_cost' => 0,
                        'activity_count' => 0,
                        'avg_rating' => 0,
                        'total_rating' => 0
                    ];
                }

                $rating = (float)($activity['rating_score'] ?? 0);
                $cost = (float)($activity['total_cost'] ?? 0);

                $strategic['strategies'][$strategyId]['total_rating'] += $rating;
                $strategic['strategies'][$strategyId]['total_cost'] += $cost;
                $strategic['strategies'][$strategyId]['activity_count']++;
            }
        }

        // Calculate average ratings for strategies
        foreach ($strategic['strategies'] as $strategyId => &$strategyData) {
            if ($strategyData['activity_count'] > 0) {
                $strategyData['avg_rating'] = $strategyData['total_rating'] / $strategyData['activity_count'];
                $strategyData['performance_score'] = $strategyData['avg_rating'] * 20; // Convert 5-star to 100-point scale
            }
        }

        // Cascade performance up the hierarchy
        $this->cascadePerformanceToKRAs($strategic, $allStrategies, $allKras);
        $this->cascadePerformanceToInvestments($strategic, $allKras, $allInvestments);
        $this->cascadePerformanceToSpecificAreas($strategic, $allInvestments, $allSpecificAreas);
        $this->cascadePerformanceToDIPs($strategic, $allSpecificAreas, $allDips);
        $this->cascadePerformanceToSPAs($strategic, $allDips, $allSpas);
        $this->cascadePerformanceToMTDPPlans($strategic, $allSpas);

        $analytics['strategic_performance'] = $strategic;
    }

    /**
     * Cascade performance from strategies to KRAs
     */
    private function cascadePerformanceToKRAs(&$strategic, $allStrategies, $allKras)
    {
        foreach ($allKras as $kra) {
            $kraId = $kra['id'];
            $strategic['kras'][$kraId] = [
                'performance_score' => 0,
                'total_cost' => 0,
                'activity_count' => 0,
                'strategy_count' => 0
            ];

            $totalPerformance = 0;
            $strategyCount = 0;

            foreach ($allStrategies as $strategy) {
                if ($strategy['kra_id'] == $kraId && isset($strategic['strategies'][$strategy['id']])) {
                    $strategyData = $strategic['strategies'][$strategy['id']];
                    $totalPerformance += $strategyData['performance_score'];
                    $strategic['kras'][$kraId]['total_cost'] += $strategyData['total_cost'];
                    $strategic['kras'][$kraId]['activity_count'] += $strategyData['activity_count'];
                    $strategyCount++;
                }
            }

            if ($strategyCount > 0) {
                $strategic['kras'][$kraId]['performance_score'] = $totalPerformance / $strategyCount;
                $strategic['kras'][$kraId]['strategy_count'] = $strategyCount;
            }
        }
    }

    /**
     * Cascade performance from KRAs to Investments
     */
    private function cascadePerformanceToInvestments(&$strategic, $allKras, $allInvestments)
    {
        foreach ($allInvestments as $investment) {
            $investmentId = $investment['id'];
            $strategic['investments'][$investmentId] = [
                'performance_score' => 0,
                'total_cost' => 0,
                'activity_count' => 0,
                'kra_count' => 0
            ];

            $totalPerformance = 0;
            $kraCount = 0;

            foreach ($allKras as $kra) {
                if ($kra['investment_id'] == $investmentId && isset($strategic['kras'][$kra['id']])) {
                    $kraData = $strategic['kras'][$kra['id']];
                    $totalPerformance += $kraData['performance_score'];
                    $strategic['investments'][$investmentId]['total_cost'] += $kraData['total_cost'];
                    $strategic['investments'][$investmentId]['activity_count'] += $kraData['activity_count'];
                    $kraCount++;
                }
            }

            if ($kraCount > 0) {
                $strategic['investments'][$investmentId]['performance_score'] = $totalPerformance / $kraCount;
                $strategic['investments'][$investmentId]['kra_count'] = $kraCount;
            }
        }
    }

    /**
     * Cascade performance from Investments to Specific Areas
     */
    private function cascadePerformanceToSpecificAreas(&$strategic, $allInvestments, $allSpecificAreas)
    {
        foreach ($allSpecificAreas as $sa) {
            $saId = $sa['id'];
            $strategic['specific_areas'][$saId] = [
                'performance_score' => 0,
                'total_cost' => 0,
                'activity_count' => 0,
                'investment_count' => 0
            ];

            $totalPerformance = 0;
            $investmentCount = 0;

            foreach ($allInvestments as $investment) {
                if ($investment['sa_id'] == $saId && isset($strategic['investments'][$investment['id']])) {
                    $investmentData = $strategic['investments'][$investment['id']];
                    $totalPerformance += $investmentData['performance_score'];
                    $strategic['specific_areas'][$saId]['total_cost'] += $investmentData['total_cost'];
                    $strategic['specific_areas'][$saId]['activity_count'] += $investmentData['activity_count'];
                    $investmentCount++;
                }
            }

            if ($investmentCount > 0) {
                $strategic['specific_areas'][$saId]['performance_score'] = $totalPerformance / $investmentCount;
                $strategic['specific_areas'][$saId]['investment_count'] = $investmentCount;
            }
        }
    }

    /**
     * Cascade performance from Specific Areas to DIPs
     */
    private function cascadePerformanceToDIPs(&$strategic, $allSpecificAreas, $allDips)
    {
        foreach ($allDips as $dip) {
            $dipId = $dip['id'];
            $strategic['dips'][$dipId] = [
                'performance_score' => 0,
                'total_cost' => 0,
                'activity_count' => 0,
                'specific_area_count' => 0
            ];

            $totalPerformance = 0;
            $saCount = 0;

            foreach ($allSpecificAreas as $sa) {
                if ($sa['dip_id'] == $dipId && isset($strategic['specific_areas'][$sa['id']])) {
                    $saData = $strategic['specific_areas'][$sa['id']];
                    $totalPerformance += $saData['performance_score'];
                    $strategic['dips'][$dipId]['total_cost'] += $saData['total_cost'];
                    $strategic['dips'][$dipId]['activity_count'] += $saData['activity_count'];
                    $saCount++;
                }
            }

            if ($saCount > 0) {
                $strategic['dips'][$dipId]['performance_score'] = $totalPerformance / $saCount;
                $strategic['dips'][$dipId]['specific_area_count'] = $saCount;
            }
        }
    }

    /**
     * Cascade performance from DIPs to SPAs
     */
    private function cascadePerformanceToSPAs(&$strategic, $allDips, $allSpas)
    {
        foreach ($allSpas as $spa) {
            $spaId = $spa['id'];
            $strategic['spas'][$spaId] = [
                'performance_score' => 0,
                'total_cost' => 0,
                'activity_count' => 0,
                'dip_count' => 0
            ];

            $totalPerformance = 0;
            $dipCount = 0;

            foreach ($allDips as $dip) {
                if ($dip['spa_id'] == $spaId && isset($strategic['dips'][$dip['id']])) {
                    $dipData = $strategic['dips'][$dip['id']];
                    $totalPerformance += $dipData['performance_score'];
                    $strategic['spas'][$spaId]['total_cost'] += $dipData['total_cost'];
                    $strategic['spas'][$spaId]['activity_count'] += $dipData['activity_count'];
                    $dipCount++;
                }
            }

            if ($dipCount > 0) {
                $strategic['spas'][$spaId]['performance_score'] = $totalPerformance / $dipCount;
                $strategic['spas'][$spaId]['dip_count'] = $dipCount;
            }
        }
    }

    /**
     * Cascade performance from SPAs to MTDP Plans
     */
    private function cascadePerformanceToMTDPPlans(&$strategic, $allSpas)
    {
        $mtdpPlans = [];

        foreach ($allSpas as $spa) {
            $mtdpId = $spa['mtdp_id'];
            if (!isset($mtdpPlans[$mtdpId])) {
                $strategic['mtdp_plans'][$mtdpId] = [
                    'performance_score' => 0,
                    'total_cost' => 0,
                    'activity_count' => 0,
                    'spa_count' => 0
                ];
                $mtdpPlans[$mtdpId] = ['total_performance' => 0, 'spa_count' => 0];
            }

            if (isset($strategic['spas'][$spa['id']])) {
                $spaData = $strategic['spas'][$spa['id']];
                $mtdpPlans[$mtdpId]['total_performance'] += $spaData['performance_score'];
                $mtdpPlans[$mtdpId]['spa_count']++;
                $strategic['mtdp_plans'][$mtdpId]['total_cost'] += $spaData['total_cost'];
                $strategic['mtdp_plans'][$mtdpId]['activity_count'] += $spaData['activity_count'];
            }
        }

        // Calculate average performance for each MTDP plan
        foreach ($mtdpPlans as $mtdpId => $planData) {
            if ($planData['spa_count'] > 0) {
                $strategic['mtdp_plans'][$mtdpId]['performance_score'] = $planData['total_performance'] / $planData['spa_count'];
                $strategic['mtdp_plans'][$mtdpId]['spa_count'] = $planData['spa_count'];
            }
        }
    }

    /**
     * Prepare performance-related chart data
     */
    private function preparePerformanceChartData($performanceAnalytics)
    {
        $chartData = [];

        // Performance by MTDP component type
        $performanceByType = [
            'strategies' => 0,
            'kras' => 0,
            'investments' => 0,
            'specific_areas' => 0,
            'dips' => 0,
            'spas' => 0,
            'mtdp_plans' => 0
        ];

        foreach ($performanceAnalytics['strategic_performance'] as $componentType => $components) {
            $totalPerformance = 0;
            $count = 0;
            foreach ($components as $component) {
                if ($component['performance_score'] > 0) {
                    $totalPerformance += $component['performance_score'];
                    $count++;
                }
            }
            if ($count > 0) {
                $performanceByType[$componentType] = $totalPerformance / $count;
            }
        }

        $chartData['performanceByType'] = $performanceByType;

        // Cost distribution by performance level
        $costByPerformance = [
            'excellent' => 0,  // 80-100%
            'good' => 0,       // 60-79%
            'average' => 0,    // 40-59%
            'poor' => 0        // 0-39%
        ];

        foreach ($performanceAnalytics['strategic_performance']['strategies'] as $strategy) {
            $score = $strategy['performance_score'];
            $cost = $strategy['total_cost'];

            if ($score >= 80) {
                $costByPerformance['excellent'] += $cost;
            } elseif ($score >= 60) {
                $costByPerformance['good'] += $cost;
            } elseif ($score >= 40) {
                $costByPerformance['average'] += $cost;
            } else {
                $costByPerformance['poor'] += $cost;
            }
        }

        $chartData['costByPerformance'] = $costByPerformance;

        return $chartData;
    }

    /**
     * Get detailed activity data for drill-down functionality
     */
    public function getLinkedActivities()
    {
        $componentType = $this->request->getGet('component_type');
        $componentId = $this->request->getGet('component_id');
        $dateFrom = $this->request->getGet('date_from');
        $dateTo = $this->request->getGet('date_to');

        if (!$componentType || !$componentId) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Component type and ID are required'
            ]);
        }

        $workplanLinkModel = new WorkplanMtdpLinkModel();
        $workplanActivityModel = new WorkplanActivityModel();
        $proposalModel = new ProposalModel();
        $workplanModel = new \App\Models\WorkplanModel();
        $branchModel = new \App\Models\BranchesModel();

        // Get links filtered by component type and ID
        $linkConditions = [];
        switch ($componentType) {
            case 'mtdp':
                $linkConditions['mtdp_id'] = $componentId;
                break;
            case 'spa':
                $linkConditions['spa_id'] = $componentId;
                break;
            case 'dip':
                $linkConditions['dip_id'] = $componentId;
                break;
            case 'specific_area':
                $linkConditions['sa_id'] = $componentId;
                break;
            case 'investment':
                $linkConditions['investment_id'] = $componentId;
                break;
            case 'kra':
                $linkConditions['kra_id'] = $componentId;
                break;
            case 'strategy':
                $linkConditions['strategies_id'] = $componentId;
                break;
            default:
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Invalid component type'
                ]);
        }

        $links = $workplanLinkModel->where($linkConditions)->findAll();

        if (empty($links)) {
            return $this->response->setJSON([
                'success' => true,
                'data' => [],
                'total' => 0
            ]);
        }

        // Get activity IDs from links
        $activityIds = array_column($links, 'workplan_activity_id');

        // Get activities
        $activities = $workplanActivityModel->whereIn('id', $activityIds)->findAll();

        // Get proposals with date filtering if needed
        $proposalConditions = ['activity_id' => $activityIds];
        if ($dateFrom || $dateTo) {
            $proposalBuilder = $proposalModel->builder();
            $proposalBuilder->whereIn('activity_id', $activityIds)
                           ->whereIn('status', ['approved', 'rated']);

            if ($dateFrom && $dateTo) {
                $proposalBuilder->groupStart()
                               ->where('(status = "approved" AND status_at >=', $dateFrom)
                               ->where('status_at <=', $dateTo . ' 23:59:59)')
                               ->orGroupStart()
                               ->where('status = "rated" AND rated_at >=', $dateFrom)
                               ->where('rated_at <=', $dateTo . ' 23:59:59')
                               ->groupEnd()
                               ->groupEnd();
            } elseif ($dateFrom) {
                $proposalBuilder->groupStart()
                               ->where('(status = "approved" AND status_at >=', $dateFrom)
                               ->orWhere('(status = "rated" AND rated_at >=', $dateFrom . ')')
                               ->groupEnd();
            } elseif ($dateTo) {
                $proposalBuilder->groupStart()
                               ->where('(status = "approved" AND status_at <=', $dateTo . ' 23:59:59)')
                               ->orWhere('(status = "rated" AND rated_at <=', $dateTo . ' 23:59:59)')
                               ->groupEnd();
            }

            $proposals = $proposalBuilder->findAll();
        } else {
            $proposals = $proposalModel->whereIn('activity_id', $activityIds)->findAll();
        }

        // Get workplans and branches for additional info
        $workplanIds = array_unique(array_column($activities, 'workplan_id'));
        $branchIds = array_unique(array_filter(array_column($activities, 'branch_id')));

        $workplans = !empty($workplanIds) ? $workplanModel->whereIn('id', $workplanIds)->findAll() : [];
        $branches = !empty($branchIds) ? $branchModel->whereIn('id', $branchIds)->findAll() : [];

        // Create lookup arrays
        $proposalsByActivityId = [];
        foreach ($proposals as $proposal) {
            $proposalsByActivityId[$proposal['activity_id']] = $proposal;
        }

        $workplansById = [];
        foreach ($workplans as $workplan) {
            $workplansById[$workplan['id']] = $workplan;
        }

        $branchesById = [];
        foreach ($branches as $branch) {
            $branchesById[$branch['id']] = $branch;
        }

        // Process the activities data
        $processedActivities = [];
        foreach ($activities as $activity) {
            $proposal = $proposalsByActivityId[$activity['id']] ?? null;
            $workplan = $workplansById[$activity['workplan_id']] ?? null;
            $branch = $branchesById[$activity['branch_id']] ?? null;

            $totalTarget = (float)($activity['q_one'] ?? 0) +
                          (float)($activity['q_two'] ?? 0) +
                          (float)($activity['q_three'] ?? 0) +
                          (float)($activity['q_four'] ?? 0);

            $processedActivities[] = [
                'id' => $activity['id'],
                'title' => $activity['title'],
                'description' => $activity['description'],
                'type' => $activity['activity_type'],
                'workplan' => $workplan['title'] ?? 'N/A',
                'branch' => $branch['name'] ?? 'N/A',
                'total_target' => $totalTarget,
                'total_cost' => (float)($activity['total_cost'] ?? 0),
                'proposal_cost' => (float)($proposal['total_cost'] ?? 0),
                'rating' => (float)($proposal['rating_score'] ?? 0),
                'status' => $activity['status'],
                'proposal_status' => $proposal['status'] ?? null,
                'rated_at' => $proposal['rated_at'] ?? null,
                'status_at' => $proposal['status_at'] ?? null
            ];
        }

        return $this->response->setJSON([
            'success' => true,
            'data' => $processedActivities,
            'total' => count($processedActivities)
        ]);
    }

}